{"name": "nextapp", "private": true, "version": "1.0.1", "type": "module", "scripts": {"dev": "vite dev", "build": "vite build", "preview": "vite preview", "prepare": "svelte-kit sync || echo ''", "check": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json", "check:watch": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json --watch", "format": "prettier --write .", "lint": "prettier --check . && eslint .", "test": "bun run format && bun run lint && bun run check"}, "devDependencies": {"@eslint/compat": "^1.2.9", "@eslint/js": "^9.25.1", "@sveltejs/adapter-node": "^5.2.12", "@sveltejs/kit": "^2.20.8", "@sveltejs/vite-plugin-svelte": "^5.0.3", "eslint": "^9.27.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-svelte": "^3.8.1", "globals": "^16.1.0", "prettier": "^3.5.3", "prettier-plugin-svelte": "^3.4.0", "svelte": "^5.33.4", "svelte-check": "^4.2.1", "typescript": "^5.8.3", "typescript-eslint": "^8.32.1", "vite": "^6.3.5"}, "dependencies": {"@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.49.8", "@tailwindcss/vite": "^4.1.7", "@u4/opencv4nodejs": "^7.1.2", "chart.js": "^4.4.9", "daisyui": "^5.0.38", "fast-csv": "^5.0.2", "lucide-svelte": "^0.511.0", "tailwindcss": "^4.1.7", "zod": "^3.25.3"}}