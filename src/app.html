<!doctype html>
<html lang="en">
	<head>
		<meta charset="utf-8" />
		<link rel="icon" href="%sveltekit.assets%/favicon.png" />
		<meta name="viewport" content="width=device-width, initial-scale=1" />
		<script>
			(function () {
				try {
					const storedTheme = localStorage.getItem('theme');
					const preferredTheme = window.matchMedia('(prefers-color-scheme: dark)').matches
						? 'dark'
						: 'light';
					let theme = storedTheme || preferredTheme;
					document.documentElement.setAttribute('data-theme', theme);
				} catch (e) {
					document.documentElement.setAttribute('data-theme', 'light');
				}
			})();
		</script>
		%sveltekit.head%
	</head>
	<body data-sveltekit-preload-data="hover">
		<div style="display: contents">%sveltekit.body%</div>
	</body>
</html>
