export type EntityType =
	| 'levels'
	| 'courses'
	| 'students'
	| 'registers'
	| 'evals'
	| 'eval_sections'
	| 'eval_questions'
	| 'eval_answers'
	| 'eval_results';

export interface Entity {
	name: string;
	label: EntityType;
}

export const entities: readonly Entity[] = [
	{ name: '<PERSON><PERSON><PERSON>', label: 'levels' },
	{ name: '<PERSON><PERSON><PERSON>', label: 'courses' },
	{ name: '<PERSON><PERSON><PERSON><PERSON><PERSON>', label: 'students' },
	{ name: '<PERSON><PERSON><PERSON><PERSON>', label: 'registers' },
	{ name: 'Evaluaciones', label: 'evals' },
	{ name: 'Secciones', label: 'eval_sections' },
	{ name: '<PERSON>gun<PERSON>', label: 'eval_questions' },
	{ name: '<PERSON><PERSON><PERSON><PERSON>', label: 'eval_answers' },
	{ name: '<PERSON>sul<PERSON><PERSON>', label: 'eval_results' }
];
